# 联系人相关业务逻辑
from app.models.contact import Contact
from app.core.database import SessionLocal
from sqlalchemy.orm import Session
from typing import Optional, List

def get_contact_by_email(db: Session, email: str) -> Optional[Contact]:
    return db.query(Contact).filter(Contact.email == email).first()

def create_contact(db: Session, name: str, email: str, phone: str = None, group: str = None, tags: str = None) -> Contact:
    contact = Contact(name=name, email=email, phone=phone, group=group, tags=tags)
    db.add(contact)
    db.commit()
    db.refresh(contact)
    return contact

def get_contacts(db: Session, skip: int = 0, limit: int = 100) -> List[Contact]:
    return db.query(Contact).offset(skip).limit(limit).all()
