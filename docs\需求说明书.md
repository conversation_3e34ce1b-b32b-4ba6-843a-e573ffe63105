# 需求说明书

## 一、项目背景

本项目旨在参考 Mautic，开发一套基于 Python（后端）和 React（前端）的现代化开源邮件营销（EDM）平台。平台面向企业、开发者和市场团队，支持自有部署，满足数据自主可控和灵活扩展需求。

## 二、核心目标

- 支持邮件营销全流程：联系人管理、邮件内容创建、分发、统计分析、自动化营销等
- 提供现代化界面和 API，便于二次开发和系统集成
- 系统易部署、易维护，具备高扩展性
- 适配多种邮件发送通道，支持自有 SMTP 和第三方服务集成

## 三、主要功能

### 1. 用户与权限管理
- 用户注册/登录（支持多用户）
- 权限分级管理（管理员/普通用户/只读用户等）

### 2. 联系人与分组管理
- 联系人导入/导出（Excel/CSV）
- 联系人标签、分组、批量编辑
- 联系人详细信息管理（自定义字段）

### 3. 邮件内容与模板
- 可视化邮件编辑器（支持拖拽/HTML/Markdown）
- 邮件模板管理/复用
- 变量插值（如用户名、活动时间等）

### 4. 邮件营销活动
- 批量邮件发送
- 邮件定时发送/计划任务
- 支持A/B测试
- 自动化营销流程（如欢迎邮件、生日祝福等）

### 5. 邮件投递与通道管理
- 支持多SMTP通道配置
- 支持主流第三方邮件服务集成
- 邮件投递队列、失败重试

### 6. 数据统计与追踪
- 邮件打开率、点击率、退订率统计
- 实时报表与可视化分析

### 7. 合规与安全
- 自动插入退订链接
- 支持 DKIM、SPF、DMARC 配置
- 联系人隐私与合规（GDPR）

### 8. API与集成
- RESTful API，支持外部系统调用
- Webhook 回调

## 四、非功能性需求

- 易于安装部署（支持 Docker）
- 支持多语言和本地化
- 良好的扩展性和模块化架构
- 高并发、大数据量处理能力

## 五、未来可能扩展

- 多渠道营销（短信、推送等）
- 更复杂的自动化流程
- 插件市场/插件机制