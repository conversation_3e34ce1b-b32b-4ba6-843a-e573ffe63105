import { Menu, Dropdown, Space, Avatar } from 'antd';
import { UserOutlined, LogoutOutlined } from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';

const Navbar = () => {
  const navigate = useNavigate();
  const username = localStorage.getItem('username') || '用户';

  const handleLogout = () => {
    localStorage.removeItem('token');
    localStorage.removeItem('username');
    navigate('/login');
  };

  const userMenu = (
    <Menu
      items={[
        {
          key: 'profile',
          icon: <UserOutlined />,
          label: '个人信息',
        },
        {
          key: 'logout',
          icon: <LogoutOutlined />,
          label: '退出登录',
          onClick: handleLogout,
        },
      ]}
    />
  );

  return (
    <div style={{ display: 'flex', justifyContent: 'space-between', padding: '0 24px' }}>
      <div className="logo" style={{ fontSize: '18px', fontWeight: 'bold' }}>
        Hermes EDM
      </div>
      <Dropdown overlay={userMenu}>
        <Space>
          <Avatar icon={<UserOutlined />} />
          <span>{username}</span>
        </Space>
      </Dropdown>
    </div>
  );
};

export default Navbar;
