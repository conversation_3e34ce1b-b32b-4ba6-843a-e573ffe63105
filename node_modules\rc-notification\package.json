{"name": "rc-notification", "version": "5.6.4", "description": "notification ui component for react", "engines": {"node": ">=8.x"}, "keywords": ["react", "react-component", "react-notification", "notification"], "homepage": "http://github.com/react-component/notification", "maintainers": ["<EMAIL>", "<EMAIL>", "<EMAIL>"], "files": ["assets/*.css", "assets/*.less", "es", "lib"], "repository": {"type": "git", "url": "**************:react-component/notification.git"}, "bugs": {"url": "http://github.com/react-component/notification/issues"}, "license": "MIT", "main": "lib/index", "module": "es/index", "typings": "es/index.d.ts", "scripts": {"start": "dumi dev", "build": "dumi build", "docs:deploy": "gh-pages -d .doc", "compile": "father build && lessc assets/index.less assets/index.css", "prepublishOnly": "npm run compile && np --yolo --no-publish --branch antd-5.x", "lint": "eslint src/ docs/examples/ --ext .tsx,.ts,.jsx,.js", "test": "vitest --watch=false", "test:watch": "vitest", "coverage": "vitest run --coverage", "now-build": "npm run build", "prepare": "husky install"}, "peerDependencies": {"react": ">=16.9.0", "react-dom": ">=16.9.0"}, "devDependencies": {"@rc-component/father-plugin": "^1.0.2", "@testing-library/jest-dom": "^6.0.0", "@testing-library/react": "^15.0.7", "@types/classnames": "^2.2.10", "@types/react": "^18.0.0", "@types/react-dom": "^18.0.0", "@types/testing-library__jest-dom": "^6.0.0", "@umijs/fabric": "^2.0.0", "@vitest/coverage-v8": "^0.34.2", "cross-env": "^7.0.0", "dumi": "^2.1.0", "eslint": "^7.8.1", "father": "^4.0.0", "gh-pages": "^3.1.0", "husky": "^8.0.3", "jsdom": "^24.0.0", "less": "^4.2.0", "lint-staged": "^14.0.1", "np": "^10.0.5", "prettier": "^3.0.2", "react": "^18.0.0", "react-dom": "^18.0.0", "typescript": "^5.4.5", "vitest": "^0.34.2"}, "dependencies": {"@babel/runtime": "^7.10.1", "classnames": "2.x", "rc-motion": "^2.9.0", "rc-util": "^5.20.1"}, "lint-staged": {"**/*.{js,jsx,tsx,ts,md,json}": ["prettier --write", "git add"]}}