import { createBrowserRouter, Navigate } from 'react-router-dom';
import MainLayout from '../layouts/MainLayout';
import Login from '../pages/Login';
import Dashboard from '../pages/Dashboard';
import Contacts from '../pages/Contacts';
import Templates from '../pages/Templates';
import Campaigns from '../pages/Campaigns';
import EmailEditor from '../pages/EmailEditor';
import Register from '../pages/Register';

const router = createBrowserRouter([
  {
    path: '/',
    element: <MainLayout />,
    children: [
      {
        index: true,
        element: <Navigate to="/dashboard" replace />,
      },
      {
        path: 'dashboard',
        element: <Dashboard />,
      },
      {
        path: 'contacts',
        element: <Contacts />,
      },
      {
        path: 'templates',
        element: <Templates />,
      },
      {
        path: 'campaigns',
        element: <Campaigns />,
      },
      {
        path: 'editor',
        element: <EmailEditor />,
      },
    ],
  },
  {
    path: '/login',
    element: <Login />, 
  },
  {
    path: '/register',
    element: <Register />, 
  },
]);

export default router;
