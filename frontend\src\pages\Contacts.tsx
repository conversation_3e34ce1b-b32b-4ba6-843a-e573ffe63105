import { useState } from 'react';
import { Table, Button, Tag, Space, Modal, message } from 'antd';
import { PlusOutlined, DeleteOutlined, EditOutlined } from '@ant-design/icons';
import PageHeader from '../components/PageHeader';
import type { Contact } from '../types';

const Contacts = () => {
  const [loading, setLoading] = useState(false);
  const [contacts, setContacts] = useState<Contact[]>([]);

  const columns = [
    {
      title: '邮箱',
      dataIndex: 'email',
      key: 'email',
    },
    {
      title: '姓名',
      dataIndex: 'first_name',
      key: 'first_name',
      render: (_: any, record: Contact) => (
        <span>{`${record.first_name || ''} ${record.last_name || ''}`}</span>
      ),
    },
    {
      title: '标签',
      dataIndex: 'tags',
      key: 'tags',
      render: (tags: string[]) => (
        <>
          {tags?.map((tag) => (
            <Tag key={tag}>{tag}</Tag>
          ))}
        </>
      ),
    },
    {
      title: '操作',
      key: 'action',
      render: (_: any, record: Contact) => (
        <Space size="middle">
          <Button
            type="text"
            icon={<EditOutlined />}
            onClick={() => handleEdit(record)}
          >
            编辑
          </Button>
          <Button
            type="text"
            danger
            icon={<DeleteOutlined />}
            onClick={() => handleDelete(record)}
          >
            删除
          </Button>
        </Space>
      ),
    },
  ];

  const handleAdd = () => {
    // TODO: 实现添加联系人功能
  };

  const handleEdit = (record: Contact) => {
    // TODO: 实现编辑联系人功能
  };

  const handleDelete = (record: Contact) => {
    Modal.confirm({
      title: '确认删除',
      content: `确定要删除联系人 ${record.email} 吗？`,
      okText: '确认',
      cancelText: '取消',
      onOk: async () => {
        try {
          // TODO: 调用删除API
          message.success('删除成功');
        } catch (error) {
          message.error('删除失败');
        }
      },
    });
  };

  return (
    <div>
      <PageHeader
        title="联系人管理"
        extra={
          <Button type="primary" icon={<PlusOutlined />} onClick={handleAdd}>
            添加联系人
          </Button>
        }
      />
      <Table
        columns={columns}
        dataSource={contacts}
        loading={loading}
        rowKey="id"
      />
    </div>
  );
};

export default Contacts;
