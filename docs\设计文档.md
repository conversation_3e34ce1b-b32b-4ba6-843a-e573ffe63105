# 设计文档

## 一、系统架构总览

### 架构图
```text
+-------------------+       +-------------------+
|    前端 (React)   | <---> |   后端 (Python)   |
+-------------------+       +-------------------+
                                   |
                                   v
                          +-------------------+
                          |    数据库         |
                          | (PostgreSQL)      |
                          +-------------------+
                                   |
                                   v
                          +-------------------+
                          | 邮件服务器/服务商  |
                          | (SMTP/第三方API)   |
                          +-------------------+
```

## 二、主要技术选型

- 前端：React + Ant Design/MUI + Axios + Redux/Context
- 后端：Python + FastAPI（或Django REST Framework）
- 数据库：PostgreSQL
- 邮件发送：内置SMTP客户端 + 支持第三方服务API
- 队列：Celery(Redis/Kafka)（邮件任务异步处理）
- 部署：Docker + docker-compose，一键部署

## 三、关键模块设计

### 1. 用户与权限模块
- 用户表、角色表设计
- JWT/OAuth2 登录鉴权机制
- RBAC 权限控制

### 2. 联系人与分组
- 联系人表、自定义字段表
- 标签、分组、批量操作逻辑

### 3. 邮件模板与内容
- 邮件模板表，支持HTML/Markdown
- 可视化编辑器：集成开源拖拽编辑器（如 grapejs/react-email-editor）

### 4. 营销活动与邮件发送
- 活动表：关联模板、收件人、发送时间、状态等
- 邮件任务表：记录单封邮件发送状态
- 邮件队列：Celery异步发信，失败重试与日志

### 5. 数据统计与追踪
- 打开/点击追踪像素 & 链接跳转
- 统计表设计（邮件、用户、活动维度）
- 图表API

### 6. 合规与安全
- 自动添加退订链接
- DKIM/SPF设置文档与校验
- 数据加密、敏感信息脱敏

### 7. API 与集成
- RESTful API 设计（OpenAPI/Swagger生成）
- Webhook 回调接口

## 四、数据库建模（部分示例）

- users（用户）
- roles（角色）
- contacts（联系人）
- contact_groups（分组/标签）
- templates（邮件模板）
- campaigns（营销活动）
- campaign_emails（单次邮件任务状态）
- statistics（统计数据）
- api_keys（第三方API集成）

## 五、部署与运维建议

- 支持docker-compose一键部署
- 提供环境变量配置说明
- 日志分级与监控集成
- 支持灰度/蓝绿发布

## 六、第三方依赖建议

- 邮件编辑器（前端）：[grapejs](https://grapesjs.com/)、[react-email-editor](https://github.com/unlayer/react-email-editor)
- 邮件发送（Python）：smtplib、aiosmtplib、django-anymail
- 队列：Celery+Redis/Kafka
- 统计追踪：自研或集成如 [posthog](https://posthog.com/)

## 七、详细技术实现方案

### 1. 前端架构

#### 1.1 技术栈详情
- React 18 (使用 TypeScript)
- Ant Design Pro 作为基础 UI 框架
- TanStack Query (原 React Query) 处理数据请求
- Zustand 状态管理
- React Email Editor 邮件编辑器
- ECharts 数据可视化
- Jest + React Testing Library 测试

#### 1.2 目录结构
```
src/
├── components/      # 通用组件
├── pages/          # 页面组件
├── services/       # API 服务
├── models/         # 数据模型
├── utils/          # 工具函数
├── hooks/          # 自定义 Hooks
├── constants/      # 常量定义
└── styles/         # 样式文件
```

### 2. 后端架构

#### 2.1 FastAPI 实现
- 采用 FastAPI 框架
- 使用 Pydantic 模型验证
- AsyncIO 异步处理
- SQLAlchemy 异步 ORM
- Alembic 数据库迁移
- pytest 测试框架

#### 2.2 目录结构
```
backend/
├── app/
│   ├── api/           # API 路由
│   ├── core/          # 核心配置
│   ├── models/        # 数据模型
│   ├── schemas/       # Pydantic 模型
│   ├── services/      # 业务逻辑
│   └── utils/         # 工具函数
├── tests/             # 测试文件
├── alembic/           # 数据库迁移
└── docker/            # Docker 配置
```

### 3. 详细数据库设计

#### 3.1 用户相关表
```sql
CREATE TABLE users (
    id SERIAL PRIMARY KEY,
    email VARCHAR(255) UNIQUE NOT NULL,
    hashed_password VARCHAR(255) NOT NULL,
    full_name VARCHAR(255),
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE roles (
    id SERIAL PRIMARY KEY,
    name VARCHAR(50) UNIQUE NOT NULL,
    permissions JSONB NOT NULL
);

CREATE TABLE user_roles (
    user_id INTEGER REFERENCES users(id),
    role_id INTEGER REFERENCES roles(id),
    PRIMARY KEY (user_id, role_id)
);
```

#### 3.2 联系人相关表
```sql
CREATE TABLE contacts (
    id SERIAL PRIMARY KEY,
    email VARCHAR(255) NOT NULL,
    first_name VARCHAR(100),
    last_name VARCHAR(100),
    custom_fields JSONB,
    tags VARCHAR(255)[],
    is_subscribed BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE contact_groups (
    id SERIAL PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    created_by INTEGER REFERENCES users(id)
);

CREATE TABLE contact_group_members (
    group_id INTEGER REFERENCES contact_groups(id),
    contact_id INTEGER REFERENCES contacts(id),
    PRIMARY KEY (group_id, contact_id)
);
```

#### 3.3 邮件营销相关表
```sql
CREATE TABLE campaigns (
    id SERIAL PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    subject VARCHAR(255) NOT NULL,
    template_id INTEGER REFERENCES templates(id),
    status VARCHAR(20) NOT NULL,
    scheduled_at TIMESTAMP WITH TIME ZONE,
    created_by INTEGER REFERENCES users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE email_templates (
    id SERIAL PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    html_content TEXT,
    variables JSONB,
    created_by INTEGER REFERENCES users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE campaign_stats (
    id SERIAL PRIMARY KEY,
    campaign_id INTEGER REFERENCES campaigns(id),
    sent_count INTEGER DEFAULT 0,
    delivered_count INTEGER DEFAULT 0,
    opened_count INTEGER DEFAULT 0,
    clicked_count INTEGER DEFAULT 0,
    bounced_count INTEGER DEFAULT 0,
    unsubscribed_count INTEGER DEFAULT 0,
    last_updated TIMESTAMP WITH TIME ZONE
);
```

### 4. API 安全实现

#### 4.1 认证机制
```python
from fastapi.security import OAuth2PasswordBearer
from jose import jwt

# JWT 配置
JWT_SECRET_KEY = config.SECRET_KEY
JWT_ALGORITHM = "HS256"
ACCESS_TOKEN_EXPIRE_MINUTES = 30

# Token 生成
def create_access_token(data: dict):
    to_encode = data.copy()
    expire = datetime.utcnow() + timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(to_encode, JWT_SECRET_KEY, algorithm=JWT_ALGORITHM)
    return encoded_jwt
```

#### 4.2 权限控制
```python
from fastapi import Depends, HTTPException
from typing import List

def check_permissions(required_permissions: List[str]):
    async def permission_checker(
        current_user: User = Depends(get_current_user)
    ):
        user_permissions = await get_user_permissions(current_user.id)
        if not all(perm in user_permissions for perm in required_permissions):
            raise HTTPException(status_code=403, detail="权限不足")
        return current_user
    return permission_checker
```

### 5. 邮件发送实现

#### 5.1 队列处理
```python
from celery import Celery

celery = Celery('tasks', broker='redis://localhost:6379/0')

@celery.task(
    bind=True,
    max_retries=3,
    default_retry_delay=5 * 60  # 5 minutes
)
def send_campaign_email(self, campaign_id: int, contact_id: int):
    try:
        # 邮件发送逻辑
        pass
    except Exception as exc:
        self.retry(exc=exc)
```

#### 5.2 失败重试机制
- 指数退避算法
- 错误分类处理
- 死信队列处理

### 6. 监控与日志

#### 6.1 Prometheus 指标
```python
from prometheus_client import Counter, Histogram

email_send_total = Counter(
    'email_send_total',
    'Total number of emails sent',
    ['status']
)

email_send_duration = Histogram(
    'email_send_duration_seconds',
    'Time spent sending email'
)
```

#### 6.2 日志配置
```python
import logging

logging.config.dictConfig({
    'version': 1,
    'disable_existing_loggers': False,
    'formatters': {
        'default': {
            'format': '%(asctime)s %(levelname)s %(name)s %(message)s'
        },
    },
    'handlers': {
        'console': {
            'class': 'logging.StreamHandler',
            'formatter': 'default',
            'level': 'INFO'
        },
        'file': {
            'class': 'logging.handlers.RotatingFileHandler',
            'filename': 'app.log',
            'maxBytes': 1024 * 1024 * 5,  # 5 MB
            'backupCount': 5,
            'formatter': 'default',
            'level': 'INFO'
        }
    },
    'root': {
        'handlers': ['console', 'file'],
        'level': 'INFO',
    },
})
```

## 八、性能优化方案

### 1. 数据库优化
- 合适的索引设计
- 查询优化
- 数据分区
- 读写分离

### 2. 缓存策略
- Redis 缓存
- 多级缓存
- 预热机制

### 3. 并发处理
- 连接池
- 异步处理
- 限流措施

## 九、测试策略

### 1. 单元测试
- 业务逻辑测试
- API 接口测试
- 数据模型测试

### 2. 集成测试
- 邮件发送测试
- 队列处理测试
- 第三方服务集成测试

### 3. 性能测试
- 负载测试
- 压力测试
- 并发测试
