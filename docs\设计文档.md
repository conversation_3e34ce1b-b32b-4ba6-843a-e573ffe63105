# 设计文档

## 一、系统架构总览

### 架构图
```text
+-------------------+       +-------------------+
|    前端 (React)   | <---> |   后端 (Python)   |
+-------------------+       +-------------------+
                                   |
                                   v
                          +-------------------+
                          |    数据库         |
                          | (PostgreSQL)      |
                          +-------------------+
                                   |
                                   v
                          +-------------------+
                          | 邮件服务器/服务商  |
                          | (SMTP/第三方API)   |
                          +-------------------+
```

## 二、主要技术选型

- 前端：React + Ant Design/MUI + Axios + Redux/Context
- 后端：Python + FastAPI（或Django REST Framework）
- 数据库：PostgreSQL
- 邮件发送：内置SMTP客户端 + 支持第三方服务API
- 队列：Celery(Redis/Kafka)（邮件任务异步处理）
- 部署：Docker + docker-compose，一键部署

## 三、关键模块设计

### 1. 用户与权限模块
- 用户表、角色表设计
- JWT/OAuth2 登录鉴权机制
- RBAC 权限控制

### 2. 联系人与分组
- 联系人表、自定义字段表
- 标签、分组、批量操作逻辑

### 3. 邮件模板与内容
- 邮件模板表，支持HTML/Markdown
- 可视化编辑器：集成开源拖拽编辑器（如 grapejs/react-email-editor）

### 4. 营销活动与邮件发送
- 活动表：关联模板、收件人、发送时间、状态等
- 邮件任务表：记录单封邮件发送状态
- 邮件队列：Celery异步发信，失败重试与日志

### 5. 数据统计与追踪
- 打开/点击追踪像素 & 链接跳转
- 统计表设计（邮件、用户、活动维度）
- 图表API

### 6. 合规与安全
- 自动添加退订链接
- DKIM/SPF设置文档与校验
- 数据加密、敏感信息脱敏

### 7. API 与集成
- RESTful API 设计（OpenAPI/Swagger生成）
- Webhook 回调接口

## 四、数据库建模（部分示例）

- users（用户）
- roles（角色）
- contacts（联系人）
- contact_groups（分组/标签）
- templates（邮件模板）
- campaigns（营销活动）
- campaign_emails（单次邮件任务状态）
- statistics（统计数据）
- api_keys（第三方API集成）

## 五、部署与运维建议

- 支持docker-compose一键部署
- 提供环境变量配置说明
- 日志分级与监控集成
- 支持灰度/蓝绿发布

## 六、第三方依赖建议

- 邮件编辑器（前端）：[grapejs](https://grapesjs.com/)、[react-email-editor](https://github.com/unlayer/react-email-editor)
- 邮件发送（Python）：smtplib、aiosmtplib、django-anymail
- 队列：Celery+Redis/Kafka
- 统计追踪：自研或集成如 [posthog](https://posthog.com/)
