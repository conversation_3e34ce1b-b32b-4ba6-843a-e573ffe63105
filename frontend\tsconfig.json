{"compilerOptions": {"target": "es5", "lib": ["dom", "dom.iterable", "esnext"], "allowJs": true, "skipLibCheck": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "strict": true, "forceConsistentCasingInFileNames": true, "noFallthroughCasesInSwitch": true, "module": "esnext", "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "jsx": "react-jsx", "baseUrl": "src", "paths": {"@/*": ["./*"], "@components/*": ["components/*"], "@pages/*": ["pages/*"], "@services/*": ["services/*"], "@utils/*": ["utils/*"], "@hooks/*": ["hooks/*"], "@types/*": ["types/*"], "@constants/*": ["constants/*"], "@assets/*": ["assets/*"]}}, "include": ["src"], "exclude": ["node_modules", "build"]}