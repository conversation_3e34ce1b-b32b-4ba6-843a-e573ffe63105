{"name": "rc-overflow", "version": "1.4.1", "description": "Auto collapse box when overflow", "keywords": ["react", "react-component", "react-overflow", "overflow", "antd", "ant-design"], "main": "./lib/index", "module": "./es/index", "files": ["assets/*.css", "assets/*.less", "es", "lib", "dist"], "homepage": "https://react-component.github.io/overflow", "repository": {"type": "git", "url": "**************:react-component/overflow.git"}, "bugs": {"url": "http://github.com/react-component/overflow/issues"}, "license": "MIT", "scripts": {"start": "dumi dev", "docs:build": "dumi build", "docs:deploy": "gh-pages -d docs-dist", "compile": "father build", "prepare": "dumi setup", "deploy": "npm run docs:build && npm run docs:deploy", "prettier": "prettier --write \"**/*.{js,jsx,tsx,ts,less,md,json}\"", "test": "rc-test", "test:coverage": "rc-test --coverage", "prepublishOnly": "npm run compile && np --no-cleanup --yolo --no-publish", "lint": "eslint src/ --ext .tsx,.ts", "lint:tsc": "tsc -p tsconfig.json --noEmit", "now-build": "npm run docs:build"}, "dependencies": {"@babel/runtime": "^7.11.1", "classnames": "^2.2.1", "rc-resize-observer": "^1.0.0", "rc-util": "^5.37.0"}, "devDependencies": {"@rc-component/father-plugin": "^1.0.0", "@testing-library/jest-dom": "^5.16.4", "@testing-library/react": "^12.0.0", "@types/classnames": "^2.2.9", "@types/enzyme": "^3.10.8", "@types/jest": "^26.0.23", "@types/react": "^16.14.2", "@types/react-dom": "^16.9.10", "@umijs/fabric": "^3.0.0", "glob": "^10.0.0", "cross-env": "^7.0.2", "dumi": "^2.0.0", "enzyme": "^3.0.0", "enzyme-adapter-react-16": "^1.0.1", "enzyme-to-json": "^3.4.0", "eslint": "^7.0.0", "father": "^4.0.0", "less": "^3.10.3", "np": "^7.0.0", "prettier": "^2.0.5", "rc-test": "^7.0", "react": "^16.0.0", "react-dom": "^16.0.0", "regenerator-runtime": "^0.13.7", "typescript": "^5.0.0"}, "peerDependencies": {"react": ">=16.9.0", "react-dom": ">=16.9.0"}, "overrides": {"cheerio": "1.0.0-rc.12"}, "cnpm": {"mode": "npm"}, "tnpm": {"mode": "npm"}}