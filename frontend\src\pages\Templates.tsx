import { Table, Button, Space, Card, Input, message } from 'antd';
import { PlusOutlined, SearchOutlined } from '@ant-design/icons';
import { useState } from 'react';
import { useNavigate } from 'react-router-dom';

const Templates = () => {
  const [loading, setLoading] = useState(false);
  const [templates, setTemplates] = useState([]);
  const navigate = useNavigate();

  const columns = [
    {
      title: '模板名称',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: '描述',
      dataIndex: 'description',
      key: 'description',
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      key: 'createdAt',
    },
    {
      title: '最后修改',
      dataIndex: 'updatedAt',
      key: 'updatedAt',
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => (
        <Space size="middle">
          <Button type="link" onClick={() => navigate(`/editor?template=${record.id}`)}>
            编辑
          </Button>
          <Button type="link" onClick={() => handleDuplicate(record.id)}>
            复制
          </Button>
          <Button type="link" danger onClick={() => handleDelete(record.id)}>
            删除
          </Button>
        </Space>
      ),
    },
  ];

  const handleDuplicate = async (id: string) => {
    try {
      setLoading(true);
      // TODO: 调用后端 API 复制模板
      message.success('模板复制成功');
    } catch (error) {
      message.error('模板复制失败');
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async (id: string) => {
    try {
      setLoading(true);
      // TODO: 调用后端 API 删除模板
      message.success('模板删除成功');
    } catch (error) {
      message.error('模板删除失败');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div>
      <Card>
        <div style={{ marginBottom: 16 }}>
          <Space>
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={() => navigate('/editor')}
            >
              创建模板
            </Button>
            <Input
              placeholder="搜索模板"
              prefix={<SearchOutlined />}
              style={{ width: 200 }}
            />
          </Space>
        </div>
        <Table
          columns={columns}
          dataSource={templates}
          loading={loading}
          rowKey="id"
        />
      </Card>
    </div>
  );
};

export default Templates;