from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from app.models.contact import Contact
from app.core.database import get_db
from pydantic import BaseModel, EmailStr
from typing import Optional, List

router = APIRouter()

class ContactCreate(BaseModel):
    name: str
    email: EmailStr
    phone: Optional[str] = None
    group: Optional[str] = None
    tags: Optional[str] = None

class ContactOut(BaseModel):
    id: int
    name: str
    email: EmailStr
    phone: Optional[str] = None
    group: Optional[str] = None
    tags: Optional[str] = None
    class Config:
        orm_mode = True

@router.post("/contacts", response_model=ContactOut)
def create_contact(contact_in: ContactCreate, db: Session = Depends(get_db)):
    existing = db.query(Contact).filter(Contact.email == contact_in.email).first()
    if existing:
        raise HTTPException(status_code=400, detail="邮箱已存在")
    contact = Contact(**contact_in.dict())
    db.add(contact)
    db.commit()
    db.refresh(contact)
    return contact

@router.get("/contacts", response_model=List[ContactOut])
def list_contacts(db: Session = Depends(get_db)):
    return db.query(Contact).all()

@router.get("/contacts/{contact_id}", response_model=ContactOut)
def get_contact(contact_id: int, db: Session = Depends(get_db)):
    contact = db.query(Contact).filter(Contact.id == contact_id).first()
    if not contact:
        raise HTTPException(status_code=404, detail="联系人不存在")
    return contact

@router.put("/contacts/{contact_id}")
def update_contact(contact_id: int, name: str = None, email: str = None, phone: str = None, group: str = None, tags: str = None, db: Session = Depends(get_db)):
    contact = db.query(Contact).filter(Contact.id == contact_id).first()
    if not contact:
        raise HTTPException(status_code=404, detail="联系人不存在")
    if name:
        contact.name = name
    if email:
        contact.email = email
    if phone:
        contact.phone = phone
    if group:
        contact.group = group
    if tags:
        contact.tags = tags
    db.commit()
    db.refresh(contact)
    return contact

@router.delete("/contacts/{contact_id}")
def delete_contact(contact_id: int, db: Session = Depends(get_db)):
    contact = db.query(Contact).filter(Contact.id == contact_id).first()
    if not contact:
        raise HTTPException(status_code=404, detail="联系人不存在")
    db.delete(contact)
    db.commit()
    return {"msg": "删除成功"}