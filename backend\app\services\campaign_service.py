# 邮件活动相关业务逻辑
from app.models.campaign import Campaign
from app.core.database import SessionLocal
from sqlalchemy.orm import Session
from typing import Optional, List

def create_campaign(db: Session, name: str, subject: str, content: str, sender: str) -> Campaign:
    campaign = Campaign(name=name, subject=subject, content=content, sender=sender)
    db.add(campaign)
    db.commit()
    db.refresh(campaign)
    return campaign

def get_campaign(db: Session, campaign_id: int) -> Optional[Campaign]:
    return db.query(Campaign).filter(Campaign.id == campaign_id).first()

def get_campaigns(db: Session, skip: int = 0, limit: int = 100) -> List[Campaign]:
    return db.query(Campaign).offset(skip).limit(limit).all()
