from fastapi import APIRouter, Depends, HTTPException
from app.services import auth
from app.models import user as user_model
from app.core import database
from fastapi.security import OAuth2Pass<PERSON>R<PERSON><PERSON>Form
from app.schemas import UserC<PERSON>, UserLogin, UserOut, Token

router = APIRouter()

from sqlalchemy.orm import Session
from app.core.database import get_db

@router.post("/register", response_model=UserOut)
def register_user(user_in: UserCreate, db: Session = Depends(get_db)):
    existing_user = db.query(user_model.User).filter(user_model.User.username == user_in.username).first()
    if existing_user:
        raise HTTPException(status_code=400, detail="用户名已存在")
    hashed_password = auth.get_password_hash(user_in.password)
    user = user_model.User(username=user_in.username, hashed_password=hashed_password, email=user_in.email)
    db.add(user)
    db.commit()
    db.refresh(user)
    return user

@router.post("/login", response_model=Token)
def login(user_in: UserLogin, db: Session = Depends(get_db)):
    user = auth.authenticate_user(db, user_in.username, user_in.password)
    if not user:
        raise HTTPException(status_code=401, detail="用户名或密码错误")
    access_token = auth.create_access_token(data={"sub": user.username})
    return {"access_token": access_token, "token_type": "bearer"}

@router.get("/me", response_model=UserOut)
def get_current_user(current_user: str = Depends(auth.get_current_user)):
    return current_user