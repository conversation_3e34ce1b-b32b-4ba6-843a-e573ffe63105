from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from app.models.campaign import Campaign
from app.core.database import get_db
from pydantic import BaseModel
from typing import Optional, List

router = APIRouter()

class CampaignCreate(BaseModel):
    name: str
    subject: str
    content: str
    sender: str

class CampaignOut(BaseModel):
    id: int
    name: str
    subject: str
    content: str
    sender: str
    status: str
    class Config:
        orm_mode = True

@router.post("/campaigns", response_model=CampaignOut)
def create_campaign(campaign_in: CampaignCreate, db: Session = Depends(get_db)):
    campaign = Campaign(**campaign_in.dict())
    db.add(campaign)
    db.commit()
    db.refresh(campaign)
    return campaign

@router.get("/campaigns", response_model=List[CampaignOut])
def list_campaigns(db: Session = Depends(get_db)):
    return db.query(Campaign).all()

@router.get("/campaigns/{campaign_id}", response_model=CampaignOut)
def get_campaign(campaign_id: int, db: Session = Depends(get_db)):
    campaign = db.query(Campaign).filter(Campaign.id == campaign_id).first()
    if not campaign:
        raise HTTPException(status_code=404, detail="活动不存在")
    return campaign

@router.put("/campaigns/{campaign_id}")
def update_campaign(campaign_id: int, name: str = None, subject: str = None, content: str = None, sender: str = None, status: str = None, db: Session = Depends(get_db)):
    campaign = db.query(Campaign).filter(Campaign.id == campaign_id).first()
    if not campaign:
        raise HTTPException(status_code=404, detail="活动不存在")
    if name:
        campaign.name = name
    if subject:
        campaign.subject = subject
    if content:
        campaign.content = content
    if sender:
        campaign.sender = sender
    if status:
        campaign.status = status
    db.commit()
    db.refresh(campaign)
    return campaign

@router.delete("/campaigns/{campaign_id}")
def delete_campaign(campaign_id: int, db: Session = Depends(get_db)):
    campaign = db.query(Campaign).filter(Campaign.id == campaign_id).first()
    if not campaign:
        raise HTTPException(status_code=404, detail="活动不存在")
    db.delete(campaign)
    db.commit()
    return {"msg": "删除成功"}