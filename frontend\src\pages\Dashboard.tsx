import { Card, Row, Col, Statistic } from 'antd';
import {
  UserOutlined,
  MailOutlined,
  CheckCircleOutlined,
  LineChartOutlined,
} from '@ant-design/icons';

const Dashboard = () => {
  return (
    <div>
      <h2>仪表盘</h2>
      <Row gutter={16}>
        <Col span={6}>
          <Card>
            <Statistic
              title="联系人总数"
              value={1234}
              prefix={<UserOutlined />}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="已发送邮件"
              value={9876}
              prefix={<MailOutlined />}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="发送成功率"
              value={98.5}
              precision={1}
              suffix="%"
              prefix={<CheckCircleOutlined />}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="平均打开率"
              value={35.8}
              precision={1}
              suffix="%"
              prefix={<LineChartOutlined />}
            />
          </Card>
        </Col>
      </Row>
      
      {/* TODO: 添加图表组件 */}
      <div style={{ marginTop: 24 }}>
        <Row gutter={16}>
          <Col span={12}>
            <Card title="近期活动">
              {/* 活动列表 */}
            </Card>
          </Col>
          <Col span={12}>
            <Card title="数据趋势">
              {/* 趋势图表 */}
            </Card>
          </Col>
        </Row>
      </div>
    </div>
  );
};

export default Dashboard;
