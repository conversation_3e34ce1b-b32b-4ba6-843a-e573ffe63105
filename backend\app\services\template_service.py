# 邮件模板相关业务逻辑
from app.models.template import Template
from app.core.database import SessionLocal
from sqlalchemy.orm import Session
from typing import Optional, List

def create_template(db: Session, name: str, content: str, description: str = None) -> Template:
    template = Template(name=name, content=content, description=description)
    db.add(template)
    db.commit()
    db.refresh(template)
    return template

def get_template(db: Session, template_id: int) -> Optional[Template]:
    return db.query(Template).filter(Template.id == template_id).first()

def get_templates(db: Session, skip: int = 0, limit: int = 100) -> List[Template]:
    return db.query(Template).offset(skip).limit(limit).all()
