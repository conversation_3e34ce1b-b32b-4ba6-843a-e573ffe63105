import { Menu } from 'antd';
import { useNavigate, useLocation } from 'react-router-dom';
import {
  DashboardOutlined,
  ContactsOutlined,
  MailOutlined,
  RocketOutlined,
} from '@ant-design/icons';

const Sidebar = () => {
  const navigate = useNavigate();
  const location = useLocation();

  const menuItems = [
    {
      key: 'dashboard',
      icon: <DashboardOutlined />, 
      label: '仪表盘',
    },
    {
      key: 'contacts',
      icon: <ContactsOutlined />, 
      label: '联系人',
    },
    {
      key: 'editor',
      icon: <MailOutlined />, 
      label: '邮件编辑器',
    },
    {
      key: 'templates',
      icon: <MailOutlined />, 
      label: '邮件模板',
    },
    {
      key: 'campaigns',
      icon: <RocketOutlined />, 
      label: '营销活动',
    },
  ];

  return (
    <Menu
      mode="inline"
      selectedKeys={[location.pathname.split('/')[1] || 'dashboard']}
      items={menuItems}
      onClick={({ key }) => navigate(`/${key}`)}
      style={{ height: '100%', borderRight: 0 }}
    />
  );
};

export default Sidebar;
