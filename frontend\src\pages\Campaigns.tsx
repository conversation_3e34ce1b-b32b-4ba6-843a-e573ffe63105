import { Table, Button, Space, Card, Input, Tag, message, Badge } from 'antd';
import { PlusOutlined, SearchOutlined } from '@ant-design/icons';
import { useState } from 'react';
import { useNavigate } from 'react-router-dom';

const Campaigns = () => {
  const [loading, setLoading] = useState(false);
  const [campaigns, setCampaigns] = useState([]);
  const navigate = useNavigate();

  // 活动状态对应的标签颜色
  const statusColors = {
    draft: 'default',
    scheduled: 'processing',
    sending: 'warning',
    sent: 'success',
    failed: 'error',
  };

  const columns = [
    {
      title: '活动名称',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status) => (
        <Tag color={statusColors[status]}>
          {status === 'draft' && '草稿'}
          {status === 'scheduled' && '已计划'}
          {status === 'sending' && '发送中'}
          {status === 'sent' && '已发送'}
          {status === 'failed' && '失败'}
        </Tag>
      ),
    },
    {
      title: '收件人数量',
      dataIndex: 'recipientCount',
      key: 'recipientCount',
    },
    {
      title: '打开率',
      dataIndex: 'openRate',
      key: 'openRate',
      render: (rate) => (rate ? `${rate}%` : '-'),
    },
    {
      title: '点击率',
      dataIndex: 'clickRate',
      key: 'clickRate',
      render: (rate) => (rate ? `${rate}%` : '-'),
    },
    {
      title: '发送时间',
      dataIndex: 'sendTime',
      key: 'sendTime',
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => (
        <Space size="middle">
          <Button type="link" onClick={() => navigate(`/campaigns/${record.id}`)}>
            查看
          </Button>
          {record.status === 'draft' && (
            <>
              <Button type="link" onClick={() => navigate(`/campaigns/${record.id}/edit`)}>
                编辑
              </Button>
              <Button type="link" onClick={() => handleSend(record.id)}>
                发送
              </Button>
            </>
          )}
          <Button type="link" onClick={() => handleDuplicate(record.id)}>
            复制
          </Button>
          <Button type="link" danger onClick={() => handleDelete(record.id)}>
            删除
          </Button>
        </Space>
      ),
    },
  ];

  const handleSend = async (id: string) => {
    try {
      setLoading(true);
      // TODO: 调用后端 API 发送活动
      message.success('活动已开始发送');
    } catch (error) {
      message.error('活动发送失败');
    } finally {
      setLoading(false);
    }
  };

  const handleDuplicate = async (id: string) => {
    try {
      setLoading(true);
      // TODO: 调用后端 API 复制活动
      message.success('活动复制成功');
    } catch (error) {
      message.error('活动复制失败');
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async (id: string) => {
    try {
      setLoading(true);
      // TODO: 调用后端 API 删除活动
      message.success('活动删除成功');
    } catch (error) {
      message.error('活动删除失败');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div>
      <Card>
        <div style={{ marginBottom: 16 }}>
          <Space>
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={() => navigate('/campaigns/new')}
            >
              创建活动
            </Button>
            <Input
              placeholder="搜索活动"
              prefix={<SearchOutlined />}
              style={{ width: 200 }}
            />
          </Space>
        </div>
        <Table
          columns={columns}
          dataSource={campaigns}
          loading={loading}
          rowKey="id"
        />
      </Card>
    </div>
  );
};

export default Campaigns;